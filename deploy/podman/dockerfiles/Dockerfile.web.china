# 使用国内镜像源优化的Web Dockerfile
# Build stage
FROM registry.cn-hangzhou.aliyuncs.com/library/node:20.19.1-alpine3.20 AS builder
WORKDIR /app

# 配置Alpine镜像源为阿里云
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 配置npm镜像源为淘宝镜像
RUN npm config set registry https://registry.npmmirror.com/

# Install pnpm globally
RUN npm install -g pnpm

# 配置pnpm镜像源
RUN pnpm config set registry https://registry.npmmirror.com/

# Set environment variables
ENV CYPRESS_INSTALL_BINARY=0
ENV NODE_OPTIONS='--max_old_space_size=8192'

# Copy package files
COPY pnpm-workspace.yaml pnpm-lock.yaml package.json turbo.json ./
COPY apps/web/package.json ./apps/web/
COPY packages/ ./packages/

# Install dependencies
RUN pnpm install --ignore-scripts

# Copy source code
COPY . .

# Build web application
RUN pnpm build:web

# Production stage - Nginx
FROM registry.cn-hangzhou.aliyuncs.com/library/nginx:1.27.3-alpine AS production

# 配置Alpine镜像源为阿里云
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# Install envsubst and bash for environment variable substitution
RUN apk add --no-cache bash

# Copy nginx configuration
COPY deploy/docker/nginx.conf /etc/nginx/conf.d/default.conf

# Copy static files from builder
WORKDIR /usr/share/nginx/html
COPY --from=builder /app/apps/web/dist .

# Create config.js template
RUN echo 'window.ENV = { \
  API_URL: "$API_URL", \
  COLLAB_URL: "$COLLAB_URL", \
  SUBSCRIPTION_ENABLED: "$SUBSCRIPTION_ENABLED", \
  STATIC_PUBLIC_ENDPOINT: "$STATIC_PUBLIC_ENDPOINT", \
  STATIC_PRIVATE_ENDPOINT: "$STATIC_PRIVATE_ENDPOINT" \
};' > /usr/share/nginx/html/config.template.js

# Create entrypoint script
RUN printf '#!/bin/bash\n\
# Set default values if not provided\n\
export API_URL=${API_URL:-"/api"}\n\
export COLLAB_URL=${COLLAB_URL:-"/collab"}\n\
export SUBSCRIPTION_ENABLED=${SUBSCRIPTION_ENABLED:-"false"}\n\
export STATIC_PUBLIC_ENDPOINT=${STATIC_PUBLIC_ENDPOINT:-"/api/v1/misc/public"}\n\
export STATIC_PRIVATE_ENDPOINT=${STATIC_PRIVATE_ENDPOINT:-"/api/v1/misc"}\n\
\n\
# Generate config.js from template\n\
envsubst < /usr/share/nginx/html/config.template.js > /usr/share/nginx/html/config.js\n\
\n\
# Start nginx\n\
exec nginx -g "daemon off;"\n' > /docker-entrypoint.sh

RUN chmod +x /docker-entrypoint.sh

# Create non-root user for security
RUN addgroup -g 1001 -S nginx-user
RUN adduser -S nginx-user -u 1001

# Change ownership of necessary directories
RUN chown -R nginx-user:nginx-user /usr/share/nginx/html
RUN chown -R nginx-user:nginx-user /var/cache/nginx
RUN chown -R nginx-user:nginx-user /var/log/nginx
RUN chown -R nginx-user:nginx-user /etc/nginx/conf.d
RUN touch /var/run/nginx.pid
RUN chown -R nginx-user:nginx-user /var/run/nginx.pid

# Switch to non-root user
USER nginx-user

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=15s --retries=3 \
    CMD curl -f http://localhost:80/ || exit 1

# Expose port 80
EXPOSE 80

# Start Nginx with our entrypoint script
ENTRYPOINT ["/docker-entrypoint.sh"]
