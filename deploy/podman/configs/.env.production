# Refly Production Environment Configuration
# 请根据实际环境修改以下配置

# ===========================================
# 基础配置
# ===========================================
NODE_ENV=production
PORT=5800
COLLAB_PORT=5801

# ===========================================
# 数据库配置
# ===========================================
DATABASE_URL=postgresql://refly:refly_password_2024@localhost:35432/refly?schema=refly
AUTO_MIGRATE_DB_SCHEMA=1

# ===========================================
# Redis配置
# ===========================================
REDIS_HOST=localhost
REDIS_PORT=36379
REDIS_PASSWORD=

# ===========================================
# MinIO对象存储配置
# ===========================================
MINIO_INTERNAL_ENDPOINT=localhost
MINIO_EXTERNAL_ENDPOINT=localhost
MINIO_PORT=39000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET_NAME=refly

# ===========================================
# Qdrant向量数据库配置
# ===========================================
QDRANT_HOST=localhost
QDRANT_PORT=36333
QDRANT_API_KEY=

# ===========================================
# SearXNG搜索引擎配置
# ===========================================
SEARXNG_BASE_URL=http://localhost:38080
SEARXNG_HOSTNAME=localhost

# ===========================================
# JWT认证配置
# ===========================================
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# ===========================================
# 文件上传配置
# ===========================================
STATIC_PUBLIC_ENDPOINT=/api/v1/misc/public
STATIC_PRIVATE_ENDPOINT=/api/v1/misc
MAX_FILE_SIZE=100MB

# ===========================================
# AI模型配置
# ===========================================
# OpenAI配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1

# 其他AI提供商配置
ANTHROPIC_API_KEY=
GOOGLE_API_KEY=
AZURE_OPENAI_API_KEY=

# ===========================================
# 邮件服务配置 (可选)
# ===========================================
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
SMTP_FROM=<EMAIL>

# ===========================================
# OAuth认证配置 (可选)
# ===========================================
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=

# ===========================================
# 监控和日志配置
# ===========================================
LOG_LEVEL=info
SENTRY_DSN=

# ===========================================
# 性能配置
# ===========================================
WORKER_CONCURRENCY=4
QUEUE_REDIS_HOST=localhost
QUEUE_REDIS_PORT=36379

# ===========================================
# 安全配置
# ===========================================
CORS_ORIGIN=http://localhost:5700
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# ===========================================
# Web前端配置
# ===========================================
API_URL=/api
COLLAB_URL=/collab
SUBSCRIPTION_ENABLED=false

# ===========================================
# 开发调试配置 (生产环境请设为false)
# ===========================================
DEBUG=false
ENABLE_SWAGGER=false
