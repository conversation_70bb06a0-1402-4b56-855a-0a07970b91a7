# SearXNG 配置文件
# 针对中国用户优化的搜索引擎配置

use_default_settings: true

general:
  debug: false
  instance_name: "Refly SearXNG"
  privacypolicy_url: false
  donation_url: false
  contact_url: false
  enable_metrics: false

brand:
  new_issue_url: false
  docs_url: false
  public_instances: false
  wiki_url: false
  issue_url: false

search:
  safe_search: 0
  autocomplete: "google"
  autocomplete_min: 4
  default_lang: "zh-CN"
  ban_time_on_fail: 5
  max_ban_time_on_fail: 120
  suspended_times:
    SearxEngineAccessDenied: 86400
    SearxEngineCaptcha: 86400
    SearxEngineTooManyRequests: 3600
    cf_SearxEngineCaptcha: 86400
    cf_SearxEngineAccessDenied: 86400
    recaptcha_SearxEngineCaptcha: 86400

server:
  port: 8080
  bind_address: "0.0.0.0"
  secret_key: "refly-searxng-secret-key-change-this"
  base_url: false
  image_proxy: true
  http_protocol_version: "1.0"
  method: "POST"
  default_http_headers:
    X-Content-Type-Options: nosniff
    X-XSS-Protection: 1; mode=block
    X-Download-Options: noopen
    X-Robots-Tag: noindex, nofollow
    Referrer-Policy: no-referrer

ui:
  static_use_hash: true
  default_locale: "zh-CN"
  query_in_title: true
  infinite_scroll: false
  center_alignment: false
  cache_url: https://web.archive.org/web/
  default_theme: simple
  theme_args:
    simple_style: auto

# 启用的搜索引擎
engines:
  - name: google
    engine: google
    shortcut: g
    use_mobile_ui: false
    
  - name: bing
    engine: bing
    shortcut: b
    
  - name: duckduckgo
    engine: duckduckgo
    shortcut: ddg
    
  - name: wikipedia
    engine: wikipedia
    shortcut: wp
    base_url: 'https://zh.wikipedia.org/'
    
  - name: github
    engine: github
    shortcut: gh
    
  - name: stackoverflow
    engine: stackoverflow
    shortcut: so
    
  - name: youtube
    engine: youtube_noapi
    shortcut: yt
    
  - name: reddit
    engine: reddit
    shortcut: re
    
  - name: arxiv
    engine: arxiv
    shortcut: arx
    
  - name: semantic scholar
    engine: semantic_scholar
    shortcut: se

# 禁用的搜索引擎
disabled_engines:
  - 'Yandex'
  - 'Yahoo'
  - 'Startpage'
  - 'Searx'

# 分类设置
categories_as_tabs:
  general:
    - general
  images:
    - images
  videos:
    - videos
  news:
    - news
  map:
    - map
  music:
    - music
  it:
    - it
  science:
    - science
  files:
    - files
  social media:
    - social media

# 本地化设置
locales:
  zh-CN: 中文 (简体)
  zh-TW: 中文 (繁體)
  en: English

# Redis 缓存配置 (如果需要)
redis:
  url: redis://localhost:6379/1

# 限流配置
limiter: false
botdetection:
  ip_limit:
    filter_link_local: false
    link_token: false
