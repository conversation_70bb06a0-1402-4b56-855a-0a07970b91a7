#!/bin/bash

# Refly Podman 部署验证脚本
# 全面验证部署状态和服务功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${PURPLE}[TEST]${NC} $1"
}

# 全局变量
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试结果记录
record_test() {
    local test_name=$1
    local result=$2
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [[ $result == "PASS" ]]; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        log_success "✅ $test_name"
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        log_error "❌ $test_name"
    fi
}

# 检查Pod状态
test_pod_status() {
    log_test "检查Pod状态"
    
    # 检查中间件Pod
    if podman pod exists refly-middleware-pod && [[ $(podman pod ps --filter name=refly-middleware-pod --format "{{.Status}}") == "Running" ]]; then
        record_test "中间件Pod状态" "PASS"
    else
        record_test "中间件Pod状态" "FAIL"
    fi
    
    # 检查主应用Pod
    if podman pod exists refly-main-pod && [[ $(podman pod ps --filter name=refly-main-pod --format "{{.Status}}") == "Running" ]]; then
        record_test "主应用Pod状态" "PASS"
    else
        record_test "主应用Pod状态" "FAIL"
    fi
}

# 检查容器状态
test_container_status() {
    log_test "检查容器状态"
    
    local containers=(
        "refly-postgres"
        "refly-redis"
        "refly-minio"
        "refly-qdrant"
        "refly-searxng"
        "refly-api"
        "refly-web"
    )
    
    for container in "${containers[@]}"; do
        if podman container exists "$container"; then
            local status=$(podman ps --filter name="$container" --format "{{.Status}}")
            if [[ $status == *"Up"* ]]; then
                record_test "$container 容器状态" "PASS"
            else
                record_test "$container 容器状态" "FAIL"
            fi
        else
            record_test "$container 容器存在性" "FAIL"
        fi
    done
}

# 检查端口连通性
test_port_connectivity() {
    log_test "检查端口连通性"
    
    local ports=(
        "PostgreSQL:35432"
        "Redis:36379"
        "MinIO API:39000"
        "MinIO Console:39001"
        "Qdrant:36333"
        "SearXNG:38080"
        "API Service:5800"
        "Web Service:5700"
    )
    
    for port_info in "${ports[@]}"; do
        local name="${port_info%:*}"
        local port="${port_info#*:}"
        
        if nc -z localhost "$port" 2>/dev/null; then
            record_test "$name 端口连通性" "PASS"
        else
            record_test "$name 端口连通性" "FAIL"
        fi
    done
}

# 检查HTTP服务响应
test_http_services() {
    log_test "检查HTTP服务响应"
    
    # 测试API健康检查
    if curl -f -s http://localhost:5800/health >/dev/null 2>&1; then
        record_test "API健康检查" "PASS"
    else
        record_test "API健康检查" "FAIL"
    fi
    
    # 测试Web服务
    if curl -f -s http://localhost:5700/ >/dev/null 2>&1; then
        record_test "Web服务响应" "PASS"
    else
        record_test "Web服务响应" "FAIL"
    fi
    
    # 测试MinIO健康检查
    if curl -f -s http://localhost:39000/minio/health/live >/dev/null 2>&1; then
        record_test "MinIO健康检查" "PASS"
    else
        record_test "MinIO健康检查" "FAIL"
    fi
    
    # 测试Qdrant健康检查
    if curl -f -s http://localhost:36333/healthz >/dev/null 2>&1; then
        record_test "Qdrant健康检查" "PASS"
    else
        record_test "Qdrant健康检查" "FAIL"
    fi
    
    # 测试SearXNG
    if curl -f -s http://localhost:38080/ >/dev/null 2>&1; then
        record_test "SearXNG服务响应" "PASS"
    else
        record_test "SearXNG服务响应" "FAIL"
    fi
}

# 检查数据库连接
test_database_connection() {
    log_test "检查数据库连接"
    
    # 测试PostgreSQL连接
    if podman exec refly-postgres psql -U refly -d refly -c "SELECT 1;" >/dev/null 2>&1; then
        record_test "PostgreSQL数据库连接" "PASS"
    else
        record_test "PostgreSQL数据库连接" "FAIL"
    fi
    
    # 测试Redis连接
    if podman exec refly-redis redis-cli ping | grep -q "PONG"; then
        record_test "Redis连接" "PASS"
    else
        record_test "Redis连接" "FAIL"
    fi
}

# 检查资源使用情况
test_resource_usage() {
    log_test "检查资源使用情况"
    
    echo ""
    echo "=== 容器资源使用情况 ==="
    podman stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" 2>/dev/null || true
    
    echo ""
    echo "=== 磁盘使用情况 ==="
    podman system df
    
    # 检查内存使用是否合理（简单检查）
    local high_memory_containers=$(podman stats --no-stream --format "{{.Name}}\t{{.MemPerc}}" 2>/dev/null | awk -F'\t' '$2 > 80 {print $1}' | wc -l)
    
    if [[ $high_memory_containers -eq 0 ]]; then
        record_test "内存使用检查" "PASS"
    else
        record_test "内存使用检查" "FAIL"
        log_warning "发现 $high_memory_containers 个容器内存使用率超过80%"
    fi
}

# 检查日志错误
test_log_errors() {
    log_test "检查服务日志"
    
    local containers=("refly-api" "refly-web")
    local error_found=false
    
    for container in "${containers[@]}"; do
        if podman container exists "$container"; then
            local error_count=$(podman logs "$container" --tail 100 2>/dev/null | grep -i -E "(error|exception|fatal)" | wc -l)
            
            if [[ $error_count -gt 0 ]]; then
                log_warning "$container 日志中发现 $error_count 个错误"
                error_found=true
            fi
        fi
    done
    
    if [[ $error_found == false ]]; then
        record_test "服务日志检查" "PASS"
    else
        record_test "服务日志检查" "FAIL"
    fi
}

# 功能测试
test_functionality() {
    log_test "功能测试"
    
    # 测试API基本功能
    local api_response=$(curl -s http://localhost:5800/api/v1/health 2>/dev/null || echo "")
    if [[ -n "$api_response" ]]; then
        record_test "API基本功能" "PASS"
    else
        record_test "API基本功能" "FAIL"
    fi
    
    # 测试Web页面加载
    local web_response=$(curl -s http://localhost:5700/ 2>/dev/null | head -c 100)
    if [[ -n "$web_response" && "$web_response" == *"html"* ]]; then
        record_test "Web页面加载" "PASS"
    else
        record_test "Web页面加载" "FAIL"
    fi
}

# 生成测试报告
generate_report() {
    echo ""
    echo "========================================"
    echo "    部署验证报告"
    echo "========================================"
    echo ""
    echo "📊 测试统计:"
    echo "   总测试数: $TOTAL_TESTS"
    echo "   通过测试: $PASSED_TESTS"
    echo "   失败测试: $FAILED_TESTS"
    echo "   成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
    echo ""
    
    if [[ $FAILED_TESTS -eq 0 ]]; then
        log_success "🎉 所有测试通过！部署验证成功"
        echo ""
        echo "✅ Refly已成功部署并正常运行"
        echo "🌐 访问地址: http://localhost:5700"
        echo "🔧 API地址: http://localhost:5800"
        return 0
    else
        log_error "❌ 部分测试失败，请检查相关服务"
        echo ""
        echo "🔍 故障排除建议:"
        echo "1. 检查容器日志: podman logs <container-name>"
        echo "2. 检查端口占用: netstat -tlnp | grep <port>"
        echo "3. 重启失败的服务: podman restart <container-name>"
        echo "4. 查看资源使用: podman stats"
        return 1
    fi
}

# 显示服务信息
show_service_info() {
    echo ""
    echo "=== 服务访问信息 ==="
    echo "🌐 用户界面:"
    echo "   Refly Web: http://localhost:5700"
    echo ""
    echo "🔧 API服务:"
    echo "   API服务: http://localhost:5800"
    echo "   API文档: http://localhost:5800/api-docs"
    echo "   健康检查: http://localhost:5800/health"
    echo ""
    echo "🗄️  管理界面:"
    echo "   MinIO控制台: http://localhost:39001 (minioadmin/minioadmin)"
    echo "   Redis管理: http://localhost:38001"
    echo "   Qdrant管理: http://localhost:36333/dashboard"
    echo "   SearXNG: http://localhost:38080"
    echo ""
    echo "📊 监控命令:"
    echo "   podman ps --pod              # 查看所有容器状态"
    echo "   podman logs -f refly-api     # 查看API日志"
    echo "   podman stats                 # 查看资源使用"
    echo "   podman pod stop refly-*-pod  # 停止服务"
}

# 主函数
main() {
    echo "========================================"
    echo "    Refly 部署验证脚本"
    echo "========================================"
    echo ""
    
    local start_time=$(date +%s)
    
    test_pod_status
    test_container_status
    test_port_connectivity
    test_http_services
    test_database_connection
    test_resource_usage
    test_log_errors
    test_functionality
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    generate_report
    show_service_info
    
    echo ""
    log_info "验证完成，耗时: ${duration}秒"
    
    # 返回适当的退出码
    if [[ $FAILED_TESTS -eq 0 ]]; then
        exit 0
    else
        exit 1
    fi
}

# 执行主函数
main "$@"
