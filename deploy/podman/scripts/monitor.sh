#!/bin/bash

# Refly Podman 监控脚本
# 实时监控服务状态和资源使用情况

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 监控间隔（秒）
MONITOR_INTERVAL=5

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 清屏函数
clear_screen() {
    clear
    echo -e "${CYAN}========================================"
    echo -e "    🔍 Refly 服务监控面板"
    echo -e "========================================"
    echo -e "    刷新间隔: ${MONITOR_INTERVAL}秒 | 按 Ctrl+C 退出"
    echo -e "========================================${NC}"
    echo ""
}

# 获取容器状态
get_container_status() {
    local container_name=$1
    
    if podman container exists "$container_name" 2>/dev/null; then
        local status=$(podman ps --filter name="$container_name" --format "{{.Status}}" 2>/dev/null)
        if [[ $status == *"Up"* ]]; then
            echo -e "${GREEN}运行中${NC}"
        else
            echo -e "${RED}已停止${NC}"
        fi
    else
        echo -e "${YELLOW}不存在${NC}"
    fi
}

# 获取端口状态
get_port_status() {
    local port=$1
    
    if nc -z localhost "$port" 2>/dev/null; then
        echo -e "${GREEN}✓${NC}"
    else
        echo -e "${RED}✗${NC}"
    fi
}

# 获取HTTP状态
get_http_status() {
    local url=$1
    
    local status_code=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")
    
    if [[ $status_code -ge 200 && $status_code -lt 300 ]]; then
        echo -e "${GREEN}$status_code${NC}"
    elif [[ $status_code -ge 300 && $status_code -lt 400 ]]; then
        echo -e "${YELLOW}$status_code${NC}"
    else
        echo -e "${RED}$status_code${NC}"
    fi
}

# 显示Pod状态
show_pod_status() {
    echo -e "${PURPLE}=== Pod 状态 ===${NC}"
    printf "%-20s %-15s %-10s\n" "Pod名称" "状态" "容器数"
    echo "----------------------------------------"
    
    local pods=("refly-middleware-pod" "refly-main-pod")
    
    for pod in "${pods[@]}"; do
        if podman pod exists "$pod" 2>/dev/null; then
            local status=$(podman pod ps --filter name="$pod" --format "{{.Status}}" 2>/dev/null)
            local container_count=$(podman ps --pod --filter pod="$pod" --format "{{.Names}}" 2>/dev/null | wc -l)
            
            if [[ $status == "Running" ]]; then
                printf "%-20s ${GREEN}%-15s${NC} %-10s\n" "$pod" "$status" "$container_count"
            else
                printf "%-20s ${RED}%-15s${NC} %-10s\n" "$pod" "$status" "$container_count"
            fi
        else
            printf "%-20s ${YELLOW}%-15s${NC} %-10s\n" "$pod" "不存在" "0"
        fi
    done
    echo ""
}

# 显示容器状态
show_container_status() {
    echo -e "${PURPLE}=== 容器状态 ===${NC}"
    printf "%-15s %-12s %-8s %-8s\n" "服务" "状态" "端口" "HTTP"
    echo "----------------------------------------"
    
    # 中间件服务
    printf "%-15s %-12s %-8s %-8s\n" "PostgreSQL" "$(get_container_status refly-postgres)" "$(get_port_status 35432)" "N/A"
    printf "%-15s %-12s %-8s %-8s\n" "Redis" "$(get_container_status refly-redis)" "$(get_port_status 36379)" "N/A"
    printf "%-15s %-12s %-8s %-8s\n" "MinIO" "$(get_container_status refly-minio)" "$(get_port_status 39000)" "$(get_http_status http://localhost:39000/minio/health/live)"
    printf "%-15s %-12s %-8s %-8s\n" "Qdrant" "$(get_container_status refly-qdrant)" "$(get_port_status 36333)" "$(get_http_status http://localhost:36333/healthz)"
    printf "%-15s %-12s %-8s %-8s\n" "SearXNG" "$(get_container_status refly-searxng)" "$(get_port_status 38080)" "$(get_http_status http://localhost:38080/)"
    
    echo "----------------------------------------"
    
    # 应用服务
    printf "%-15s %-12s %-8s %-8s\n" "API" "$(get_container_status refly-api)" "$(get_port_status 5800)" "$(get_http_status http://localhost:5800/health)"
    printf "%-15s %-12s %-8s %-8s\n" "Web" "$(get_container_status refly-web)" "$(get_port_status 5700)" "$(get_http_status http://localhost:5700/)"
    
    echo ""
}

# 显示资源使用情况
show_resource_usage() {
    echo -e "${PURPLE}=== 资源使用情况 ===${NC}"
    
    # 获取资源统计
    local stats_output=$(podman stats --no-stream --format "{{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" 2>/dev/null | grep "refly-")
    
    if [[ -n "$stats_output" ]]; then
        printf "%-15s %-10s %-15s %-10s\n" "容器" "CPU%" "内存使用" "内存%"
        echo "----------------------------------------"
        
        while IFS=$'\t' read -r name cpu mem mem_perc; do
            local service_name=${name#refly-}
            
            # CPU使用率颜色
            local cpu_color=$NC
            local cpu_val=${cpu%\%}
            if (( $(echo "$cpu_val > 80" | bc -l) 2>/dev/null )); then
                cpu_color=$RED
            elif (( $(echo "$cpu_val > 50" | bc -l) 2>/dev/null )); then
                cpu_color=$YELLOW
            else
                cpu_color=$GREEN
            fi
            
            # 内存使用率颜色
            local mem_color=$NC
            local mem_val=${mem_perc%\%}
            if (( $(echo "$mem_val > 80" | bc -l) 2>/dev/null )); then
                mem_color=$RED
            elif (( $(echo "$mem_val > 50" | bc -l) 2>/dev/null )); then
                mem_color=$YELLOW
            else
                mem_color=$GREEN
            fi
            
            printf "%-15s ${cpu_color}%-10s${NC} %-15s ${mem_color}%-10s${NC}\n" "$service_name" "$cpu" "$mem" "$mem_perc"
        done <<< "$stats_output"
    else
        echo "无法获取资源使用统计"
    fi
    
    echo ""
}

# 显示系统信息
show_system_info() {
    echo -e "${PURPLE}=== 系统信息 ===${NC}"
    
    # 系统负载
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | xargs)
    echo "系统负载: $load_avg"
    
    # 内存使用
    local mem_info=$(free -h | awk 'NR==2{printf "已用: %s / 总计: %s (%.1f%%)", $3, $2, $3/$2*100}')
    echo "内存使用: $mem_info"
    
    # 磁盘使用
    local disk_usage=$(df -h . | awk 'NR==2{printf "已用: %s / 总计: %s (%s)", $3, $2, $5}')
    echo "磁盘使用: $disk_usage"
    
    # Podman版本
    local podman_version=$(podman --version | awk '{print $3}')
    echo "Podman版本: $podman_version"
    
    echo ""
}

# 显示最近的错误日志
show_recent_errors() {
    echo -e "${PURPLE}=== 最近错误 (最后5分钟) ===${NC}"
    
    local containers=("refly-api" "refly-web")
    local has_errors=false
    
    for container in "${containers[@]}"; do
        if podman container exists "$container" 2>/dev/null; then
            local errors=$(podman logs --since "5m" "$container" 2>/dev/null | grep -i -E "(error|exception|fatal)" | tail -3)
            
            if [[ -n "$errors" ]]; then
                echo -e "${YELLOW}$container:${NC}"
                echo "$errors" | while read -r line; do
                    echo "  $line"
                done
                has_errors=true
            fi
        fi
    done
    
    if [[ $has_errors == false ]]; then
        echo -e "${GREEN}无错误日志${NC}"
    fi
    
    echo ""
}

# 显示网络连接状态
show_network_status() {
    echo -e "${PURPLE}=== 网络连接状态 ===${NC}"
    
    local endpoints=(
        "Web界面:http://localhost:5700"
        "API服务:http://localhost:5800"
        "MinIO控制台:http://localhost:39001"
        "Qdrant管理:http://localhost:36333"
    )
    
    for endpoint in "${endpoints[@]}"; do
        local name="${endpoint%:*}"
        local url="${endpoint#*:}"
        local status=$(get_http_status "$url")
        
        printf "%-15s %s\n" "$name" "$status"
    done
    
    echo ""
}

# 显示帮助信息
show_help() {
    echo "Refly Podman 监控工具"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -i, --interval N    设置刷新间隔（秒，默认5）"
    echo "  --once             只显示一次，不循环"
    echo "  --simple           简化显示模式"
    echo "  -h, --help         显示帮助信息"
    echo ""
    echo "快捷键:"
    echo "  Ctrl+C             退出监控"
    echo ""
}

# 简化显示模式
simple_monitor() {
    clear_screen
    show_pod_status
    show_container_status
    
    echo -e "${BLUE}提示: 使用 --help 查看更多选项${NC}"
}

# 完整监控模式
full_monitor() {
    clear_screen
    show_pod_status
    show_container_status
    show_resource_usage
    show_system_info
    show_recent_errors
    show_network_status
    
    echo -e "${BLUE}最后更新: $(date '+%Y-%m-%d %H:%M:%S')${NC}"
}

# 主函数
main() {
    local once_mode=false
    local simple_mode=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -i|--interval)
                MONITOR_INTERVAL="$2"
                shift 2
                ;;
            --once)
                once_mode=true
                shift
                ;;
            --simple)
                simple_mode=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 验证间隔时间
    if ! [[ "$MONITOR_INTERVAL" =~ ^[0-9]+$ ]] || [[ $MONITOR_INTERVAL -lt 1 ]]; then
        log_error "无效的刷新间隔: $MONITOR_INTERVAL"
        exit 1
    fi
    
    # 检查必要命令
    if ! command -v bc &> /dev/null; then
        log_warning "bc命令未安装，某些功能可能不可用"
    fi
    
    # 执行监控
    if [[ $once_mode == true ]]; then
        if [[ $simple_mode == true ]]; then
            simple_monitor
        else
            full_monitor
        fi
    else
        # 循环监控
        while true; do
            if [[ $simple_mode == true ]]; then
                simple_monitor
            else
                full_monitor
            fi
            
            sleep "$MONITOR_INTERVAL"
        done
    fi
}

# 执行主函数
main "$@"
