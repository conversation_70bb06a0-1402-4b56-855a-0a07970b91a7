#!/bin/bash

# Refly Podman 一键部署脚本
# 自动执行完整的部署流程

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 获取脚本目录
SCRIPT_DIR="$(dirname "$(realpath "$0")")"

# 显示欢迎信息
show_welcome() {
    echo ""
    echo "========================================"
    echo "    🚀 Refly Podman 一键部署脚本"
    echo "========================================"
    echo ""
    echo "本脚本将自动完成以下步骤:"
    echo "1. 创建Pod和网络配置"
    echo "2. 构建应用镜像"
    echo "3. 启动中间件服务"
    echo "4. 启动应用服务"
    echo "5. 验证部署状态"
    echo ""
    echo "预计耗时: 10-20分钟（取决于网络速度）"
    echo ""
}

# 检查系统要求
check_system_requirements() {
    log_step "检查系统要求"
    
    # 检查Podman
    if ! command -v podman &> /dev/null; then
        log_error "Podman未安装，请先安装Podman"
        echo "安装命令参考:"
        echo "  CentOS/RHEL: sudo dnf install podman"
        echo "  Ubuntu: sudo apt install podman"
        echo "  macOS: brew install podman"
        exit 1
    fi
    
    # 检查curl
    if ! command -v curl &> /dev/null; then
        log_error "curl未安装，请先安装curl"
        exit 1
    fi
    
    # 检查nc (netcat)
    if ! command -v nc &> /dev/null; then
        log_warning "nc (netcat)未安装，部分网络检查功能可能不可用"
    fi
    
    # 检查内存
    local total_mem=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    if [[ $total_mem -lt 4096 ]]; then
        log_warning "系统内存少于4GB，可能影响性能"
    fi
    
    # 检查磁盘空间
    local available_space=$(df . | awk 'NR==2 {print $4}')
    if [[ $available_space -lt 10485760 ]]; then  # 10GB in KB
        log_warning "可用磁盘空间少于10GB，可能影响部署"
    fi
    
    log_success "系统要求检查完成"
}

# 检查环境配置
check_environment_config() {
    log_step "检查环境配置"
    
    local env_file="$SCRIPT_DIR/../configs/.env.production"
    
    if [[ ! -f "$env_file" ]]; then
        log_warning "环境配置文件不存在，将使用默认配置"
        log_info "请在部署完成后根据需要修改: $env_file"
    else
        log_success "环境配置文件已存在"
    fi
    
    # 检查重要配置项
    if [[ -f "$env_file" ]]; then
        local missing_configs=()
        
        # 检查数据库配置
        if ! grep -q "DATABASE_URL" "$env_file"; then
            missing_configs+=("DATABASE_URL")
        fi
        
        # 检查JWT密钥
        if grep -q "your-super-secret-jwt-key-change-this-in-production" "$env_file"; then
            log_warning "请修改JWT_SECRET为安全的密钥"
        fi
        
        if [[ ${#missing_configs[@]} -gt 0 ]]; then
            log_warning "缺少以下配置项: ${missing_configs[*]}"
        fi
    fi
}

# 执行部署步骤
execute_deployment_step() {
    local step_name=$1
    local script_name=$2
    local step_number=$3
    
    echo ""
    log_step "步骤 $step_number: $step_name"
    echo "----------------------------------------"
    
    local start_time=$(date +%s)
    
    if [[ -x "$SCRIPT_DIR/$script_name" ]]; then
        if "$SCRIPT_DIR/$script_name"; then
            local end_time=$(date +%s)
            local duration=$((end_time - start_time))
            log_success "$step_name 完成 (耗时: ${duration}秒)"
        else
            log_error "$step_name 失败"
            exit 1
        fi
    else
        log_error "脚本不存在或不可执行: $SCRIPT_DIR/$script_name"
        exit 1
    fi
}

# 显示部署进度
show_progress() {
    local current=$1
    local total=$2
    local step_name=$3
    
    local percentage=$((current * 100 / total))
    local filled=$((percentage / 5))
    local empty=$((20 - filled))
    
    printf "\r${BLUE}[进度]${NC} ["
    printf "%*s" $filled | tr ' ' '='
    printf "%*s" $empty | tr ' ' '-'
    printf "] %d%% - %s" $percentage "$step_name"
    
    if [[ $current -eq $total ]]; then
        echo ""
    fi
}

# 处理中断信号
cleanup_on_interrupt() {
    echo ""
    log_warning "部署被中断"
    log_info "清理资源..."
    
    # 尝试清理可能创建的资源
    "$SCRIPT_DIR/cleanup.sh" 2>/dev/null || true
    
    exit 1
}

# 显示部署结果
show_deployment_result() {
    local success=$1
    
    echo ""
    echo "========================================"
    if [[ $success -eq 0 ]]; then
        echo "    🎉 部署成功完成！"
        echo "========================================"
        echo ""
        echo "✅ Refly已成功部署并正常运行"
        echo ""
        echo "🌐 访问地址:"
        echo "   Web界面: http://localhost:5700"
        echo "   API服务: http://localhost:5800"
        echo "   API文档: http://localhost:5800/api-docs"
        echo ""
        echo "🔧 管理界面:"
        echo "   MinIO控制台: http://localhost:39001"
        echo "   Redis管理: http://localhost:38001"
        echo "   Qdrant管理: http://localhost:36333/dashboard"
        echo ""
        echo "📚 管理命令:"
        echo "   查看状态: podman ps --pod"
        echo "   查看日志: podman logs -f refly-api"
        echo "   停止服务: podman pod stop refly-main-pod refly-middleware-pod"
        echo "   启动服务: podman pod start refly-middleware-pod refly-main-pod"
        echo ""
        echo "🛠️  故障排除:"
        echo "   运行验证: $SCRIPT_DIR/05-verify-deployment.sh"
        echo "   查看日志: $SCRIPT_DIR/show-logs.sh"
        echo "   完全清理: $SCRIPT_DIR/cleanup.sh"
    else
        echo "    ❌ 部署失败"
        echo "========================================"
        echo ""
        echo "🔍 故障排除建议:"
        echo "1. 检查系统资源是否充足"
        echo "2. 检查网络连接是否正常"
        echo "3. 查看详细错误日志"
        echo "4. 运行清理脚本后重试: $SCRIPT_DIR/cleanup.sh"
        echo ""
        echo "📞 获取帮助:"
        echo "   项目文档: https://github.com/refly-ai/refly"
        echo "   提交Issue: https://github.com/refly-ai/refly/issues"
    fi
    echo "========================================"
}

# 主函数
main() {
    # 设置中断处理
    trap cleanup_on_interrupt INT TERM
    
    show_welcome
    
    # 询问用户确认
    read -p "是否继续部署？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
    
    local deployment_start_time=$(date +%s)
    
    # 预检查
    check_system_requirements
    check_environment_config
    
    # 执行部署步骤
    local steps=(
        "创建Pod和网络配置:01-create-pods.sh"
        "构建应用镜像:02-build-images.sh"
        "启动中间件服务:03-run-middleware.sh"
        "启动应用服务:04-run-applications.sh"
        "验证部署状态:05-verify-deployment.sh"
    )
    
    local total_steps=${#steps[@]}
    local current_step=0
    
    for step in "${steps[@]}"; do
        current_step=$((current_step + 1))
        local step_name="${step%:*}"
        local script_name="${step#*:}"
        
        show_progress $current_step $total_steps "$step_name"
        execute_deployment_step "$step_name" "$script_name" $current_step
    done
    
    local deployment_end_time=$(date +%s)
    local total_duration=$((deployment_end_time - deployment_start_time))
    
    echo ""
    log_success "部署流程完成！总耗时: $((total_duration / 60))分$((total_duration % 60))秒"
    
    # 显示最终结果
    show_deployment_result 0
}

# 执行主函数
main "$@"
