#!/bin/bash

# Refly Podman 镜像构建脚本
# 构建API和Web应用的Docker镜像

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取项目根目录
get_project_root() {
    local script_dir="$(dirname "$(dirname "$(dirname "$(realpath "$0")")")")"
    echo "$script_dir"
}

# 检查必要文件
check_prerequisites() {
    local project_root=$(get_project_root)
    
    log_info "检查构建前置条件..."
    
    # 检查Dockerfile
    if [[ ! -f "$project_root/deploy/podman/dockerfiles/Dockerfile.api.china" ]]; then
        log_error "API Dockerfile不存在: $project_root/deploy/podman/dockerfiles/Dockerfile.api.china"
        exit 1
    fi
    
    if [[ ! -f "$project_root/deploy/podman/dockerfiles/Dockerfile.web.china" ]]; then
        log_error "Web Dockerfile不存在: $project_root/deploy/podman/dockerfiles/Dockerfile.web.china"
        exit 1
    fi
    
    # 检查源码目录
    if [[ ! -d "$project_root/apps/api" ]]; then
        log_error "API源码目录不存在: $project_root/apps/api"
        exit 1
    fi
    
    if [[ ! -d "$project_root/apps/web" ]]; then
        log_error "Web源码目录不存在: $project_root/apps/web"
        exit 1
    fi
    
    log_success "前置条件检查通过"
}

# 构建API镜像
build_api_image() {
    local project_root=$(get_project_root)
    
    log_info "开始构建API镜像..."
    
    cd "$project_root"
    
    # 构建API镜像
    podman build \
        --file deploy/podman/dockerfiles/Dockerfile.api.china \
        --tag refly-api:latest \
        --tag refly-api:$(date +%Y%m%d-%H%M%S) \
        --build-arg TARGETARCH=$(uname -m | sed 's/x86_64/amd64/;s/aarch64/arm64/') \
        --layers \
        --force-rm \
        .
    
    log_success "API镜像构建完成"
}

# 构建Web镜像
build_web_image() {
    local project_root=$(get_project_root)
    
    log_info "开始构建Web镜像..."
    
    cd "$project_root"
    
    # 构建Web镜像
    podman build \
        --file deploy/podman/dockerfiles/Dockerfile.web.china \
        --tag refly-web:latest \
        --tag refly-web:$(date +%Y%m%d-%H%M%S) \
        --layers \
        --force-rm \
        .
    
    log_success "Web镜像构建完成"
}

# 清理构建缓存
cleanup_build_cache() {
    log_info "清理构建缓存..."
    
    # 清理悬空镜像
    podman image prune -f
    
    log_success "构建缓存清理完成"
}

# 验证镜像
verify_images() {
    log_info "验证构建的镜像..."
    
    echo ""
    echo "=== 构建的镜像列表 ==="
    podman images | grep -E "(refly-api|refly-web)"
    
    echo ""
    echo "=== 镜像详细信息 ==="
    
    # 检查API镜像
    if podman image exists refly-api:latest; then
        local api_size=$(podman images refly-api:latest --format "{{.Size}}")
        log_success "API镜像构建成功，大小: $api_size"
    else
        log_error "API镜像构建失败"
        exit 1
    fi
    
    # 检查Web镜像
    if podman image exists refly-web:latest; then
        local web_size=$(podman images refly-web:latest --format "{{.Size}}")
        log_success "Web镜像构建成功，大小: $web_size"
    else
        log_error "Web镜像构建失败"
        exit 1
    fi
}

# 显示构建统计
show_build_stats() {
    echo ""
    echo "=== 构建统计 ==="
    echo "总镜像数量: $(podman images | grep -E "(refly-api|refly-web)" | wc -l)"
    echo "磁盘使用情况:"
    podman system df
}

# 主函数
main() {
    echo "========================================"
    echo "    Refly Podman 镜像构建脚本"
    echo "========================================"
    echo ""
    
    local start_time=$(date +%s)
    
    check_prerequisites
    build_api_image
    build_web_image
    cleanup_build_cache
    verify_images
    show_build_stats
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo ""
    log_success "镜像构建完成！总耗时: ${duration}秒"
    log_info "下一步: 运行 ./03-run-middleware.sh 启动中间件服务"
}

# 执行主函数
main "$@"
