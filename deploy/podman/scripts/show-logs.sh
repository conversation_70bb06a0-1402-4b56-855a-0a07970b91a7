#!/bin/bash

# Refly Podman 日志查看脚本
# 方便查看各个服务的日志

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Refly Podman 日志查看工具"
    echo ""
    echo "用法: $0 [选项] [服务名]"
    echo ""
    echo "服务名:"
    echo "  api        - API服务日志"
    echo "  web        - Web服务日志"
    echo "  postgres   - PostgreSQL数据库日志"
    echo "  redis      - Redis缓存日志"
    echo "  minio      - MinIO对象存储日志"
    echo "  qdrant     - Qdrant向量数据库日志"
    echo "  searxng    - SearXNG搜索引擎日志"
    echo "  all        - 所有服务日志"
    echo ""
    echo "选项:"
    echo "  -f, --follow     跟踪日志输出"
    echo "  -t, --tail N     显示最后N行日志 (默认: 100)"
    echo "  --since TIME     显示指定时间后的日志"
    echo "  --errors         只显示错误日志"
    echo "  -h, --help       显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 api                    # 查看API服务日志"
    echo "  $0 -f api                 # 跟踪API服务日志"
    echo "  $0 -t 50 postgres         # 查看PostgreSQL最后50行日志"
    echo "  $0 --errors all           # 查看所有服务的错误日志"
    echo "  $0 --since '1h ago' api   # 查看API服务1小时内的日志"
}

# 检查容器是否存在
check_container_exists() {
    local container_name=$1
    
    if ! podman container exists "$container_name" 2>/dev/null; then
        log_error "容器 $container_name 不存在"
        return 1
    fi
    
    return 0
}

# 显示单个服务日志
show_service_logs() {
    local service=$1
    local container_name="refly-$service"
    local follow=$2
    local tail_lines=$3
    local since_time=$4
    local errors_only=$5
    
    if ! check_container_exists "$container_name"; then
        return 1
    fi
    
    log_info "显示 $service 服务日志..."
    echo "========================================"
    
    # 构建podman logs命令
    local cmd="podman logs"
    
    if [[ $follow == true ]]; then
        cmd="$cmd -f"
    fi
    
    if [[ -n $tail_lines ]]; then
        cmd="$cmd --tail $tail_lines"
    fi
    
    if [[ -n $since_time ]]; then
        cmd="$cmd --since '$since_time'"
    fi
    
    cmd="$cmd $container_name"
    
    # 执行命令
    if [[ $errors_only == true ]]; then
        eval "$cmd" 2>&1 | grep -i -E "(error|exception|fatal|panic|critical)" --color=always || true
    else
        eval "$cmd"
    fi
}

# 显示所有服务日志
show_all_logs() {
    local follow=$1
    local tail_lines=$2
    local since_time=$3
    local errors_only=$4
    
    local services=("api" "web" "postgres" "redis" "minio" "qdrant" "searxng")
    
    if [[ $follow == true ]]; then
        log_warning "跟踪模式下只显示API和Web服务日志"
        services=("api" "web")
    fi
    
    for service in "${services[@]}"; do
        local container_name="refly-$service"
        
        if check_container_exists "$container_name"; then
            echo ""
            echo -e "${PURPLE}=== $service 服务日志 ===${NC}"
            show_service_logs "$service" false "$tail_lines" "$since_time" "$errors_only"
        fi
    done
    
    if [[ $follow == true ]]; then
        echo ""
        log_info "开始跟踪API和Web服务日志 (Ctrl+C退出)..."
        podman logs -f refly-api &
        podman logs -f refly-web &
        wait
    fi
}

# 显示日志统计
show_log_stats() {
    log_info "日志统计信息"
    echo "========================================"
    
    local services=("api" "web" "postgres" "redis" "minio" "qdrant" "searxng")
    
    printf "%-12s %-10s %-10s %-10s\n" "服务" "总行数" "错误数" "警告数"
    echo "----------------------------------------"
    
    for service in "${services[@]}"; do
        local container_name="refly-$service"
        
        if check_container_exists "$container_name"; then
            local total_lines=$(podman logs "$container_name" 2>/dev/null | wc -l)
            local error_lines=$(podman logs "$container_name" 2>/dev/null | grep -i -E "(error|exception|fatal)" | wc -l)
            local warning_lines=$(podman logs "$container_name" 2>/dev/null | grep -i "warning" | wc -l)
            
            printf "%-12s %-10s %-10s %-10s\n" "$service" "$total_lines" "$error_lines" "$warning_lines"
        else
            printf "%-12s %-10s %-10s %-10s\n" "$service" "N/A" "N/A" "N/A"
        fi
    done
}

# 导出日志
export_logs() {
    local output_dir="logs_export_$(date +%Y%m%d_%H%M%S)"
    
    log_info "导出日志到目录: $output_dir"
    mkdir -p "$output_dir"
    
    local services=("api" "web" "postgres" "redis" "minio" "qdrant" "searxng")
    
    for service in "${services[@]}"; do
        local container_name="refly-$service"
        
        if check_container_exists "$container_name"; then
            log_info "导出 $service 服务日志..."
            podman logs "$container_name" > "$output_dir/${service}.log" 2>&1
        fi
    done
    
    # 创建摘要文件
    cat > "$output_dir/README.txt" << EOF
Refly 服务日志导出
导出时间: $(date)
导出目录: $output_dir

文件说明:
- api.log: API服务日志
- web.log: Web服务日志
- postgres.log: PostgreSQL数据库日志
- redis.log: Redis缓存日志
- minio.log: MinIO对象存储日志
- qdrant.log: Qdrant向量数据库日志
- searxng.log: SearXNG搜索引擎日志

使用方法:
- 查看特定服务日志: cat api.log
- 搜索错误信息: grep -i error *.log
- 按时间排序: ls -lt *.log
EOF
    
    log_success "日志导出完成: $output_dir"
    echo "总文件数: $(ls -1 "$output_dir" | wc -l)"
    echo "总大小: $(du -sh "$output_dir" | cut -f1)"
}

# 主函数
main() {
    local service=""
    local follow=false
    local tail_lines="100"
    local since_time=""
    local errors_only=false
    local show_stats=false
    local export_mode=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--follow)
                follow=true
                shift
                ;;
            -t|--tail)
                tail_lines="$2"
                shift 2
                ;;
            --since)
                since_time="$2"
                shift 2
                ;;
            --errors)
                errors_only=true
                shift
                ;;
            --stats)
                show_stats=true
                shift
                ;;
            --export)
                export_mode=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            api|web|postgres|redis|minio|qdrant|searxng|all)
                service="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定服务，显示帮助
    if [[ -z $service && $show_stats == false && $export_mode == false ]]; then
        show_help
        exit 0
    fi
    
    echo "========================================"
    echo "    Refly 日志查看工具"
    echo "========================================"
    echo ""
    
    # 执行相应操作
    if [[ $show_stats == true ]]; then
        show_log_stats
    elif [[ $export_mode == true ]]; then
        export_logs
    elif [[ $service == "all" ]]; then
        show_all_logs "$follow" "$tail_lines" "$since_time" "$errors_only"
    else
        show_service_logs "$service" "$follow" "$tail_lines" "$since_time" "$errors_only"
    fi
}

# 执行主函数
main "$@"
