#!/bin/bash

# Refly Podman 清理脚本
# 停止并删除所有相关的Pod、容器、镜像和数据

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示警告信息
show_warning() {
    echo ""
    echo "========================================"
    echo "    ⚠️  Refly Podman 清理脚本"
    echo "========================================"
    echo ""
    echo "⚠️  警告: 此操作将删除以下内容:"
    echo "   • 所有Refly相关的Pod和容器"
    echo "   • 所有Refly相关的镜像"
    echo "   • 所有数据卷和配置文件"
    echo "   • 数据库、文件存储等所有数据"
    echo ""
    echo "❗ 此操作不可逆，请确保已备份重要数据！"
    echo ""
}

# 停止并删除Pod
cleanup_pods() {
    log_info "清理Pod..."
    
    local pods=("refly-main-pod" "refly-middleware-pod")
    
    for pod in "${pods[@]}"; do
        if podman pod exists "$pod" 2>/dev/null; then
            log_info "停止Pod: $pod"
            podman pod stop "$pod" 2>/dev/null || true
            
            log_info "删除Pod: $pod"
            podman pod rm "$pod" 2>/dev/null || true
            
            log_success "Pod $pod 已清理"
        else
            log_info "Pod $pod 不存在，跳过"
        fi
    done
}

# 删除容器
cleanup_containers() {
    log_info "清理容器..."
    
    local containers=(
        "refly-api"
        "refly-web"
        "refly-postgres"
        "refly-redis"
        "refly-minio"
        "refly-qdrant"
        "refly-searxng"
    )
    
    for container in "${containers[@]}"; do
        if podman container exists "$container" 2>/dev/null; then
            log_info "停止容器: $container"
            podman stop "$container" 2>/dev/null || true
            
            log_info "删除容器: $container"
            podman rm "$container" 2>/dev/null || true
            
            log_success "容器 $container 已清理"
        else
            log_info "容器 $container 不存在，跳过"
        fi
    done
}

# 删除镜像
cleanup_images() {
    log_info "清理镜像..."
    
    local images=(
        "refly-api:latest"
        "refly-web:latest"
    )
    
    for image in "${images[@]}"; do
        if podman image exists "$image" 2>/dev/null; then
            log_info "删除镜像: $image"
            podman rmi "$image" 2>/dev/null || true
            log_success "镜像 $image 已删除"
        else
            log_info "镜像 $image 不存在，跳过"
        fi
    done
    
    # 删除带时间戳的镜像
    log_info "清理带时间戳的镜像..."
    podman images --format "{{.Repository}}:{{.Tag}}" | grep -E "refly-(api|web):[0-9]" | while read -r image; do
        if [[ -n "$image" ]]; then
            log_info "删除镜像: $image"
            podman rmi "$image" 2>/dev/null || true
        fi
    done
}

# 清理数据卷
cleanup_volumes() {
    log_info "清理数据卷..."
    
    local script_dir="$(dirname "$(dirname "$(realpath "$0")")")"
    local volumes_dir="$script_dir/volumes"
    
    if [[ -d "$volumes_dir" ]]; then
        log_warning "删除数据目录: $volumes_dir"
        rm -rf "$volumes_dir"
        log_success "数据目录已删除"
    else
        log_info "数据目录不存在，跳过"
    fi
}

# 清理网络
cleanup_networks() {
    log_info "清理网络..."
    
    # Podman会自动清理未使用的网络
    podman network prune -f 2>/dev/null || true
    
    log_success "网络清理完成"
}

# 清理系统缓存
cleanup_system_cache() {
    log_info "清理系统缓存..."
    
    # 清理悬空镜像
    podman image prune -f 2>/dev/null || true
    
    # 清理构建缓存
    podman system prune -f 2>/dev/null || true
    
    log_success "系统缓存清理完成"
}

# 验证清理结果
verify_cleanup() {
    log_info "验证清理结果..."
    
    echo ""
    echo "=== 剩余的Refly相关资源 ==="
    
    # 检查Pod
    local remaining_pods=$(podman pod ps --filter name=refly --format "{{.Name}}" 2>/dev/null | wc -l)
    echo "剩余Pod数量: $remaining_pods"
    
    # 检查容器
    local remaining_containers=$(podman ps -a --filter name=refly --format "{{.Names}}" 2>/dev/null | wc -l)
    echo "剩余容器数量: $remaining_containers"
    
    # 检查镜像
    local remaining_images=$(podman images --filter reference=refly-* --format "{{.Repository}}:{{.Tag}}" 2>/dev/null | wc -l)
    echo "剩余镜像数量: $remaining_images"
    
    if [[ $remaining_pods -eq 0 && $remaining_containers -eq 0 && $remaining_images -eq 0 ]]; then
        log_success "✅ 清理完成，所有Refly资源已删除"
    else
        log_warning "⚠️  仍有部分资源未清理完成"
        
        if [[ $remaining_pods -gt 0 ]]; then
            echo "剩余Pod:"
            podman pod ps --filter name=refly 2>/dev/null || true
        fi
        
        if [[ $remaining_containers -gt 0 ]]; then
            echo "剩余容器:"
            podman ps -a --filter name=refly 2>/dev/null || true
        fi
        
        if [[ $remaining_images -gt 0 ]]; then
            echo "剩余镜像:"
            podman images --filter reference=refly-* 2>/dev/null || true
        fi
    fi
}

# 显示清理统计
show_cleanup_stats() {
    echo ""
    echo "=== 清理后系统状态 ==="
    echo "磁盘使用情况:"
    podman system df 2>/dev/null || true
    
    echo ""
    echo "当前运行的容器:"
    podman ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || true
}

# 强制清理模式
force_cleanup() {
    log_warning "执行强制清理模式..."
    
    # 强制停止所有相关容器
    podman ps -a --filter name=refly --format "{{.Names}}" 2>/dev/null | while read -r container; do
        if [[ -n "$container" ]]; then
            podman kill "$container" 2>/dev/null || true
            podman rm -f "$container" 2>/dev/null || true
        fi
    done
    
    # 强制删除所有相关Pod
    podman pod ps --filter name=refly --format "{{.Name}}" 2>/dev/null | while read -r pod; do
        if [[ -n "$pod" ]]; then
            podman pod kill "$pod" 2>/dev/null || true
            podman pod rm -f "$pod" 2>/dev/null || true
        fi
    done
    
    log_success "强制清理完成"
}

# 主函数
main() {
    show_warning
    
    # 检查参数
    local force_mode=false
    local skip_confirmation=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force)
                force_mode=true
                shift
                ;;
            --yes)
                skip_confirmation=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --force    强制清理模式"
                echo "  --yes      跳过确认提示"
                echo "  -h, --help 显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 用户确认
    if [[ $skip_confirmation == false ]]; then
        read -p "确定要继续清理吗？(输入 'yes' 确认): " -r
        if [[ $REPLY != "yes" ]]; then
            log_info "清理已取消"
            exit 0
        fi
    fi
    
    local start_time=$(date +%s)
    
    if [[ $force_mode == true ]]; then
        force_cleanup
    fi
    
    cleanup_pods
    cleanup_containers
    cleanup_images
    cleanup_volumes
    cleanup_networks
    cleanup_system_cache
    verify_cleanup
    show_cleanup_stats
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo ""
    log_success "清理完成！耗时: ${duration}秒"
    
    echo ""
    echo "💡 提示:"
    echo "   如需重新部署，请运行: ./deploy-all.sh"
    echo "   如需部分清理，请手动使用podman命令"
}

# 执行主函数
main "$@"
