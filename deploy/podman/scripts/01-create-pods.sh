#!/bin/bash

# Refly Podman Pod 创建脚本
# 创建Pod和网络配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Podman是否安装
check_podman() {
    if ! command -v podman &> /dev/null; then
        log_error "Podman未安装，请先安装Podman"
        exit 1
    fi
    
    local version=$(podman --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
    log_info "检测到Podman版本: $version"
}

# 创建数据目录
create_directories() {
    log_info "创建数据目录..."
    
    local base_dir="$(dirname "$(dirname "$(realpath "$0")")")"
    local volumes_dir="$base_dir/volumes"
    
    mkdir -p "$volumes_dir"/{db_data,redis_data,minio_data,qdrant_data,searxng_config,logs}
    
    # 设置权限
    chmod 755 "$volumes_dir"
    chmod -R 777 "$volumes_dir"/{db_data,redis_data,minio_data,qdrant_data}
    
    log_success "数据目录创建完成: $volumes_dir"
}

# 清理现有Pod（如果存在）
cleanup_existing_pods() {
    log_info "清理现有Pod..."
    
    # 停止并删除现有Pod
    for pod in refly-middleware-pod refly-main-pod; do
        if podman pod exists "$pod" 2>/dev/null; then
            log_warning "发现现有Pod: $pod，正在清理..."
            podman pod stop "$pod" 2>/dev/null || true
            podman pod rm "$pod" 2>/dev/null || true
            log_success "Pod $pod 已清理"
        fi
    done
}

# 创建中间件Pod
create_middleware_pod() {
    log_info "创建中间件Pod: refly-middleware-pod"
    
    podman pod create \
        --name refly-middleware-pod \
        --publish 35432:5432 \
        --publish 36379:6379 \
        --publish 38001:8001 \
        --publish 39000:9000 \
        --publish 39001:9001 \
        --publish 36333:6333 \
        --publish 36334:6334 \
        --publish 38080:8080 \
        --network bridge
    
    log_success "中间件Pod创建完成"
}

# 创建主应用Pod
create_main_pod() {
    log_info "创建主应用Pod: refly-main-pod"
    
    podman pod create \
        --name refly-main-pod \
        --publish 5700:80 \
        --publish 5800:5800 \
        --publish 5801:5801 \
        --network bridge
    
    log_success "主应用Pod创建完成"
}

# 验证Pod创建
verify_pods() {
    log_info "验证Pod创建状态..."
    
    echo ""
    echo "=== Pod列表 ==="
    podman pod ps
    
    echo ""
    echo "=== 端口映射 ==="
    echo "中间件服务端口:"
    echo "  - PostgreSQL: 35432"
    echo "  - Redis: 36379, 38001"
    echo "  - MinIO: 39000, 39001"
    echo "  - Qdrant: 36333, 36334"
    echo "  - SearXNG: 38080"
    echo ""
    echo "应用服务端口:"
    echo "  - Web前端: 5700"
    echo "  - API服务: 5800, 5801"
    
    log_success "Pod创建验证完成"
}

# 主函数
main() {
    echo "========================================"
    echo "    Refly Podman Pod 创建脚本"
    echo "========================================"
    echo ""
    
    check_podman
    create_directories
    cleanup_existing_pods
    create_middleware_pod
    create_main_pod
    verify_pods
    
    echo ""
    log_success "Pod创建完成！"
    log_info "下一步: 运行 ./02-build-images.sh 构建镜像"
}

# 执行主函数
main "$@"
