#!/bin/bash

# Refly Podman 应用服务启动脚本
# 启动API和Web前端服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取项目目录
get_project_root() {
    local script_dir="$(dirname "$(dirname "$(realpath "$0")")")"
    local project_root="$(dirname "$(dirname "$script_dir")")"
    echo "$project_root"
}

# 等待服务健康检查
wait_for_service() {
    local service_name=$1
    local health_check_cmd=$2
    local max_attempts=30
    local attempt=1
    
    log_info "等待 $service_name 服务启动..."
    
    while [ $attempt -le $max_attempts ]; do
        if eval "$health_check_cmd" &>/dev/null; then
            log_success "$service_name 服务已就绪"
            return 0
        fi
        
        echo -n "."
        sleep 3
        attempt=$((attempt + 1))
    done
    
    log_error "$service_name 服务启动超时"
    return 1
}

# 检查中间件服务状态
check_middleware_services() {
    log_info "检查中间件服务状态..."
    
    local services=(
        "PostgreSQL:35432"
        "Redis:36379"
        "MinIO:39000"
        "Qdrant:36333"
    )
    
    for service in "${services[@]}"; do
        local name="${service%:*}"
        local port="${service#*:}"
        
        if ! nc -z localhost "$port" 2>/dev/null; then
            log_error "$name 服务未就绪 (端口 $port)，请先启动中间件服务"
            exit 1
        fi
    done
    
    log_success "中间件服务状态检查通过"
}

# 启动API服务
start_api_service() {
    local project_root=$(get_project_root)
    local env_file="$project_root/deploy/podman/configs/.env.production"
    
    log_info "启动Refly API服务..."
    
    # 检查环境配置文件
    if [[ ! -f "$env_file" ]]; then
        log_error "环境配置文件不存在: $env_file"
        exit 1
    fi
    
    # 检查API镜像
    if ! podman image exists refly-api:latest; then
        log_error "API镜像不存在，请先运行 ./02-build-images.sh"
        exit 1
    fi
    
    podman run -d \
        --name refly-api \
        --pod refly-main-pod \
        --env-file "$env_file" \
        --env DATABASE_URL="postgresql://refly:refly_password_2024@localhost:35432/refly?schema=refly" \
        --env REDIS_HOST=localhost \
        --env REDIS_PORT=36379 \
        --env MINIO_INTERNAL_ENDPOINT=localhost \
        --env MINIO_EXTERNAL_ENDPOINT=localhost \
        --env MINIO_PORT=39000 \
        --env QDRANT_HOST=localhost \
        --env QDRANT_PORT=36333 \
        --env SEARXNG_BASE_URL=http://localhost:38080 \
        --env AUTO_MIGRATE_DB_SCHEMA=1 \
        --env NODE_ENV=production \
        --memory=4g \
        --cpus=2 \
        --restart=always \
        --health-cmd="curl -f http://localhost:5800/health || exit 1" \
        --health-interval=30s \
        --health-timeout=10s \
        --health-retries=3 \
        --health-start-period=60s \
        refly-api:latest
    
    wait_for_service "API" "curl -f http://localhost:5800/health"
}

# 启动Web服务
start_web_service() {
    log_info "启动Refly Web前端服务..."
    
    # 检查Web镜像
    if ! podman image exists refly-web:latest; then
        log_error "Web镜像不存在，请先运行 ./02-build-images.sh"
        exit 1
    fi
    
    podman run -d \
        --name refly-web \
        --pod refly-main-pod \
        --env API_URL="/api" \
        --env COLLAB_URL="/collab" \
        --env SUBSCRIPTION_ENABLED="false" \
        --env STATIC_PUBLIC_ENDPOINT="/api/v1/misc/public" \
        --env STATIC_PRIVATE_ENDPOINT="/api/v1/misc" \
        --memory=1g \
        --cpus=1 \
        --restart=always \
        --health-cmd="curl -f http://localhost:80/ || exit 1" \
        --health-interval=30s \
        --health-timeout=10s \
        --health-retries=3 \
        --health-start-period=30s \
        refly-web:latest
    
    wait_for_service "Web" "curl -f http://localhost:5700/"
}

# 配置反向代理（如果需要）
setup_reverse_proxy() {
    log_info "配置服务间通信..."
    
    # 这里可以添加额外的网络配置或反向代理设置
    # 由于使用Pod，容器间已经可以通过localhost通信
    
    log_success "服务间通信配置完成"
}

# 验证应用服务
verify_application_services() {
    log_info "验证应用服务状态..."
    
    echo ""
    echo "=== 应用容器状态 ==="
    podman ps --pod --filter "pod=refly-main-pod"
    
    echo ""
    echo "=== 服务健康检查 ==="
    
    # 检查API服务
    if curl -f http://localhost:5800/health &>/dev/null; then
        log_success "API服务正常 (http://localhost:5800)"
    else
        log_warning "API服务可能未就绪"
    fi
    
    # 检查Web服务
    if curl -f http://localhost:5700/ &>/dev/null; then
        log_success "Web服务正常 (http://localhost:5700)"
    else
        log_warning "Web服务可能未就绪"
    fi
    
    echo ""
    echo "=== 资源使用情况 ==="
    podman stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" \
        refly-api refly-web 2>/dev/null || true
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "========================================"
    echo "    Refly 部署完成"
    echo "========================================"
    echo ""
    echo "🎉 恭喜！Refly已成功部署"
    echo ""
    echo "📱 访问地址:"
    echo "   Web界面: http://localhost:5700"
    echo "   API服务: http://localhost:5800"
    echo "   API文档: http://localhost:5800/api-docs"
    echo ""
    echo "🔧 管理界面:"
    echo "   MinIO控制台: http://localhost:39001"
    echo "   Redis管理: http://localhost:38001"
    echo "   Qdrant管理: http://localhost:36333/dashboard"
    echo ""
    echo "📊 监控命令:"
    echo "   查看日志: podman logs -f refly-api"
    echo "   查看状态: podman ps --pod"
    echo "   资源监控: podman stats"
    echo ""
    echo "🛠️  管理命令:"
    echo "   停止服务: podman pod stop refly-main-pod"
    echo "   启动服务: podman pod start refly-main-pod"
    echo "   重启服务: podman pod restart refly-main-pod"
}

# 主函数
main() {
    echo "========================================"
    echo "    Refly 应用服务启动脚本"
    echo "========================================"
    echo ""
    
    # 检查Pod是否存在
    if ! podman pod exists refly-main-pod; then
        log_error "主应用Pod不存在，请先运行 ./01-create-pods.sh"
        exit 1
    fi
    
    local start_time=$(date +%s)
    
    check_middleware_services
    start_api_service
    start_web_service
    setup_reverse_proxy
    verify_application_services
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    show_deployment_info
    
    echo ""
    log_success "应用服务启动完成！总耗时: ${duration}秒"
    log_info "运行 ./05-verify-deployment.sh 进行完整的部署验证"
}

# 执行主函数
main "$@"
