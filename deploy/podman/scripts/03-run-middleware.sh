#!/bin/bash

# Refly Podman 中间件服务启动脚本
# 启动PostgreSQL, Redis, MinIO, Qdrant, SearXNG等中间件服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取项目根目录和数据目录
get_directories() {
    local script_dir="$(dirname "$(dirname "$(realpath "$0")")")"
    local volumes_dir="$script_dir/volumes"
    echo "$volumes_dir"
}

# 等待服务健康检查
wait_for_service() {
    local service_name=$1
    local health_check_cmd=$2
    local max_attempts=30
    local attempt=1
    
    log_info "等待 $service_name 服务启动..."
    
    while [ $attempt -le $max_attempts ]; do
        if eval "$health_check_cmd" &>/dev/null; then
            log_success "$service_name 服务已就绪"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "$service_name 服务启动超时"
    return 1
}

# 启动PostgreSQL
start_postgresql() {
    local volumes_dir=$(get_directories)
    
    log_info "启动PostgreSQL数据库..."
    
    podman run -d \
        --name refly-postgres \
        --pod refly-middleware-pod \
        --volume "$volumes_dir/db_data:/var/lib/postgresql/data:Z" \
        --env POSTGRES_DB=refly \
        --env POSTGRES_USER=refly \
        --env POSTGRES_PASSWORD=refly_password_2024 \
        --env PGDATA=/var/lib/postgresql/data/pgdata \
        --memory=2g \
        --cpus=2 \
        --restart=always \
        --health-cmd="pg_isready -U refly -d refly" \
        --health-interval=10s \
        --health-timeout=5s \
        --health-retries=3 \
        --health-start-period=30s \
        postgres:16-alpine
    
    wait_for_service "PostgreSQL" "podman exec refly-postgres pg_isready -U refly -d refly"
}

# 启动Redis
start_redis() {
    local volumes_dir=$(get_directories)
    
    log_info "启动Redis缓存服务..."
    
    podman run -d \
        --name refly-redis \
        --pod refly-middleware-pod \
        --volume "$volumes_dir/redis_data:/data:Z" \
        --memory=1g \
        --cpus=1 \
        --restart=always \
        --health-cmd="redis-cli ping" \
        --health-interval=10s \
        --health-timeout=5s \
        --health-retries=3 \
        --health-start-period=10s \
        redis:7-alpine \
        redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    
    wait_for_service "Redis" "podman exec refly-redis redis-cli ping"
}

# 启动MinIO
start_minio() {
    local volumes_dir=$(get_directories)
    
    log_info "启动MinIO对象存储服务..."
    
    podman run -d \
        --name refly-minio \
        --pod refly-middleware-pod \
        --volume "$volumes_dir/minio_data:/data:Z" \
        --env MINIO_ROOT_USER=minioadmin \
        --env MINIO_ROOT_PASSWORD=minioadmin \
        --memory=1g \
        --cpus=1 \
        --restart=always \
        --health-cmd="curl -f http://localhost:9000/minio/health/live" \
        --health-interval=30s \
        --health-timeout=10s \
        --health-retries=3 \
        --health-start-period=10s \
        minio/minio:RELEASE.2025-01-20T14-49-07Z \
        server /data --console-address ":9001"
    
    wait_for_service "MinIO" "curl -f http://localhost:39000/minio/health/live"
}

# 启动Qdrant
start_qdrant() {
    local volumes_dir=$(get_directories)
    
    log_info "启动Qdrant向量数据库..."
    
    podman run -d \
        --name refly-qdrant \
        --pod refly-middleware-pod \
        --volume "$volumes_dir/qdrant_data:/qdrant/storage:Z" \
        --memory=2g \
        --cpus=2 \
        --restart=always \
        --health-cmd="curl -s http://localhost:6333/healthz | grep -q 'healthz check passed'" \
        --health-interval=30s \
        --health-timeout=10s \
        --health-retries=3 \
        --health-start-period=10s \
        qdrant/qdrant:v1.13.1
    
    wait_for_service "Qdrant" "curl -s http://localhost:36333/healthz | grep -q 'healthz check passed'"
}

# 启动SearXNG
start_searxng() {
    local volumes_dir=$(get_directories)
    
    log_info "启动SearXNG搜索引擎..."
    
    # 创建SearXNG配置目录
    mkdir -p "$volumes_dir/searxng_config"
    
    # 如果配置文件不存在，创建默认配置
    if [[ ! -f "$volumes_dir/searxng_config/settings.yml" ]]; then
        cat > "$volumes_dir/searxng_config/settings.yml" << 'EOF'
use_default_settings: true
server:
  secret_key: "your-secret-key-change-this"
  limiter: false
  image_proxy: true
search:
  safe_search: 0
  autocomplete: "google"
  default_lang: "zh-CN"
ui:
  static_use_hash: true
  default_locale: "zh-CN"
  query_in_title: true
EOF
    fi
    
    podman run -d \
        --name refly-searxng \
        --pod refly-middleware-pod \
        --volume "$volumes_dir/searxng_config:/etc/searxng:Z" \
        --env SEARXNG_BASE_URL=http://localhost:38080/ \
        --env UWSGI_WORKERS=4 \
        --env UWSGI_THREADS=4 \
        --memory=512m \
        --cpus=1 \
        --restart=always \
        searxng/searxng:latest
    
    sleep 10  # SearXNG需要更长的启动时间
    wait_for_service "SearXNG" "curl -f http://localhost:38080/"
}

# 验证所有服务
verify_services() {
    log_info "验证中间件服务状态..."
    
    echo ""
    echo "=== 容器状态 ==="
    podman ps --pod --filter "pod=refly-middleware-pod"
    
    echo ""
    echo "=== 服务健康检查 ==="
    
    # 检查各服务端口
    local services=(
        "PostgreSQL:35432"
        "Redis:36379"
        "MinIO:39000"
        "MinIO Console:39001"
        "Qdrant:36333"
        "SearXNG:38080"
    )
    
    for service in "${services[@]}"; do
        local name="${service%:*}"
        local port="${service#*:}"
        
        if nc -z localhost "$port" 2>/dev/null; then
            log_success "$name 服务正常 (端口 $port)"
        else
            log_warning "$name 服务可能未就绪 (端口 $port)"
        fi
    done
}

# 主函数
main() {
    echo "========================================"
    echo "    Refly 中间件服务启动脚本"
    echo "========================================"
    echo ""
    
    # 检查Pod是否存在
    if ! podman pod exists refly-middleware-pod; then
        log_error "中间件Pod不存在，请先运行 ./01-create-pods.sh"
        exit 1
    fi
    
    local start_time=$(date +%s)
    
    start_postgresql
    start_redis
    start_minio
    start_qdrant
    start_searxng
    verify_services
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo ""
    log_success "中间件服务启动完成！总耗时: ${duration}秒"
    log_info "下一步: 运行 ./04-run-applications.sh 启动应用服务"
    
    echo ""
    echo "=== 服务访问地址 ==="
    echo "PostgreSQL: localhost:35432"
    echo "Redis: localhost:36379"
    echo "MinIO API: http://localhost:39000"
    echo "MinIO Console: http://localhost:39001"
    echo "Qdrant: http://localhost:36333"
    echo "SearXNG: http://localhost:38080"
}

# 执行主函数
main "$@"
