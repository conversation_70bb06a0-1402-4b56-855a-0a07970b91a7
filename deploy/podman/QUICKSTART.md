# Refly Podman 快速开始指南

## 🚀 5分钟快速部署

### 前置要求

- **操作系统**: Linux (推荐 CentOS 8+/Ubuntu 20.04+)
- **内存**: 最少 8GB，推荐 16GB+
- **磁盘**: 最少 20GB 可用空间
- **Podman**: 版本 4.0+

### 安装 Podman

```bash
# CentOS/RHEL
sudo dnf install podman

# Ubuntu/Debian
sudo apt update && sudo apt install podman

# macOS
brew install podman
```

### 一键部署

```bash
# 1. 克隆项目
git clone https://github.com/refly-ai/refly.git
cd refly/deploy/podman

# 2. 执行一键部署
./scripts/deploy-all.sh
```

### 验证部署

部署完成后，访问以下地址：

- **Web界面**: http://localhost:5700
- **API服务**: http://localhost:5800
- **API文档**: http://localhost:5800/api-docs

## 📋 分步部署

如果需要更精细的控制，可以分步执行：

```bash
# 1. 创建Pod和网络
./scripts/01-create-pods.sh

# 2. 构建应用镜像
./scripts/02-build-images.sh

# 3. 启动中间件服务
./scripts/03-run-middleware.sh

# 4. 启动应用服务
./scripts/04-run-applications.sh

# 5. 验证部署状态
./scripts/05-verify-deployment.sh
```

## 🔧 常用管理命令

### 查看状态

```bash
# 查看所有Pod状态
podman pod ps

# 查看所有容器状态
podman ps --pod

# 实时监控
./scripts/monitor.sh
```

### 查看日志

```bash
# 查看所有服务日志
./scripts/show-logs.sh all

# 查看特定服务日志
./scripts/show-logs.sh api

# 跟踪日志输出
./scripts/show-logs.sh -f api
```

### 服务控制

```bash
# 停止服务
podman pod stop refly-main-pod refly-middleware-pod

# 启动服务
podman pod start refly-middleware-pod refly-main-pod

# 重启服务
podman pod restart refly-main-pod
```

## ⚙️ 配置自定义

### 环境变量配置

编辑 `configs/.env.production` 文件：

```bash
# 数据库配置
DATABASE_URL=postgresql://refly:your_password@localhost:35432/refly

# AI模型配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1

# JWT密钥（生产环境必须修改）
JWT_SECRET=your-secure-secret-key-here
```

### 端口映射修改

如果默认端口冲突，可以修改脚本中的端口映射：

```bash
# 在相应脚本中修改端口映射
--publish 8080:80      # Web服务
--publish 8081:5800    # API服务
--publish 15432:5432   # PostgreSQL
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :5700
   
   # 修改端口映射或停止占用进程
   ```

2. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   
   # 调整容器资源限制
   # 在脚本中修改 --memory 参数
   ```

3. **镜像构建失败**
   ```bash
   # 清理构建缓存
   podman system prune -f
   
   # 重新构建
   ./scripts/02-build-images.sh
   ```

4. **服务启动失败**
   ```bash
   # 查看详细日志
   ./scripts/show-logs.sh --errors all
   
   # 检查容器状态
   podman ps -a
   ```

### 诊断命令

```bash
# 运行完整验证
./scripts/05-verify-deployment.sh

# 查看资源使用
podman stats

# 检查网络连接
curl -f http://localhost:5800/health
```

## 🧹 清理环境

```bash
# 完全清理（删除所有数据）
./scripts/cleanup.sh

# 强制清理
./scripts/cleanup.sh --force --yes
```

## 📊 监控面板

启动实时监控面板：

```bash
# 完整监控模式
./scripts/monitor.sh

# 简化监控模式
./scripts/monitor.sh --simple

# 单次显示
./scripts/monitor.sh --once
```

## 🔐 安全配置

### 生产环境建议

1. **修改默认密码**
   ```bash
   # 修改数据库密码
   POSTGRES_PASSWORD=your-secure-password
   
   # 修改MinIO密钥
   MINIO_ROOT_USER=your-username
   MINIO_ROOT_PASSWORD=your-secure-password
   ```

2. **配置防火墙**
   ```bash
   # 只开放必要端口
   sudo firewall-cmd --add-port=5700/tcp --permanent
   sudo firewall-cmd --reload
   ```

3. **启用HTTPS**
   - 配置反向代理（Nginx/Apache）
   - 申请SSL证书
   - 修改环境变量中的URL配置

## 📚 更多资源

- **完整文档**: [README.md](README.md)
- **项目主页**: https://github.com/refly-ai/refly
- **问题反馈**: https://github.com/refly-ai/refly/issues

## 🆘 获取帮助

如果遇到问题：

1. 查看 [README.md](README.md) 详细文档
2. 运行诊断脚本：`./scripts/05-verify-deployment.sh`
3. 查看日志：`./scripts/show-logs.sh --errors all`
4. 在GitHub提交Issue并附上错误日志

---

**祝您使用愉快！** 🎉
