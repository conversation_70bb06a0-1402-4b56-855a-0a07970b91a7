# Refly Podman Pod 部署指南

本指南提供了使用Podman以Pod模式部署Refly项目的完整方案，针对国内网络环境进行了优化。

## 架构概述

部署采用两个Pod架构：
- **refly-middleware-pod**: 包含所有中间件服务（数据库、缓存、存储等）
- **refly-main-pod**: 包含主要应用服务（API和Web前端）

## 系统要求

- Podman 4.0+
- 系统内存: 最少8GB，推荐16GB+
- 磁盘空间: 最少20GB可用空间
- 操作系统: Linux (推荐CentOS 8+/Ubuntu 20.04+)

## 快速部署

### 1. 准备环境

```bash
# 克隆项目
git clone https://github.com/refly-ai/refly.git
cd refly

# 进入部署目录
cd deploy/podman

# 赋予脚本执行权限
chmod +x scripts/*.sh
```

### 2. 配置环境变量

```bash
# 复制并编辑环境配置文件
cp configs/.env.example configs/.env.production
vim configs/.env.production
```

### 3. 执行部署

```bash
# 一键部署（推荐）
./scripts/deploy-all.sh

# 或者分步部署
./scripts/01-create-pods.sh
./scripts/02-build-images.sh
./scripts/03-run-middleware.sh
./scripts/04-run-applications.sh
```

### 4. 验证部署

```bash
./scripts/05-verify-deployment.sh
```

## 服务访问

部署完成后，可通过以下地址访问服务：

- **Web界面**: http://localhost:5700
- **API服务**: http://localhost:5800
- **MinIO控制台**: http://localhost:39001
- **Redis管理**: http://localhost:38001

## 目录结构

```
deploy/podman/
├── configs/           # 配置文件
├── dockerfiles/       # 优化的Dockerfile
├── scripts/          # 部署脚本
├── volumes/          # 数据卷挂载点
└── logs/            # 日志文件
```

## 管理命令

### 启动服务
```bash
podman pod start refly-middleware-pod
podman pod start refly-main-pod
```

### 停止服务
```bash
podman pod stop refly-main-pod
podman pod stop refly-middleware-pod
```

### 查看状态
```bash
podman pod ps
podman ps --pod
```

### 查看日志
```bash
podman logs refly-api
podman logs refly-web
```

## 故障排除

### 常见问题

1. **端口冲突**: 检查端口是否被占用
2. **内存不足**: 增加系统内存或调整容器资源限制
3. **镜像拉取失败**: 检查网络连接和镜像源配置

### 日志查看

```bash
# 查看所有容器日志
./scripts/show-logs.sh

# 查看特定服务日志
podman logs -f refly-api
```

## 性能优化

### 资源限制配置

在生产环境中，建议为每个容器设置适当的资源限制：

- **API服务**: CPU 2核, 内存 4GB
- **Web服务**: CPU 1核, 内存 1GB
- **PostgreSQL**: CPU 2核, 内存 2GB
- **Redis**: CPU 1核, 内存 1GB
- **MinIO**: CPU 1核, 内存 1GB

### 数据备份

```bash
# 备份数据库
./scripts/backup-database.sh

# 备份MinIO数据
./scripts/backup-minio.sh
```

## 安全配置

1. 修改默认密码
2. 配置防火墙规则
3. 启用HTTPS（生产环境）
4. 定期更新镜像

## 更新升级

```bash
# 更新应用
./scripts/update-application.sh

# 更新中间件
./scripts/update-middleware.sh
```

## 清理环境

```bash
# 停止并删除所有容器和Pod
./scripts/cleanup.sh
```

## 高级功能

### 监控和日志

```bash
# 查看所有服务日志
./scripts/show-logs.sh all

# 跟踪API服务日志
./scripts/show-logs.sh -f api

# 查看错误日志
./scripts/show-logs.sh --errors all

# 导出日志
./scripts/show-logs.sh --export
```

### 备份和恢复

```bash
# 备份数据
./scripts/backup-data.sh

# 恢复数据
./scripts/restore-data.sh backup_20240101.tar.gz
```

### 扩展配置

#### 自定义环境变量
编辑 `configs/.env.production` 文件来自定义配置：

```bash
# 数据库配置
DATABASE_URL=postgresql://user:pass@localhost:35432/refly

# AI模型配置
OPENAI_API_KEY=your-api-key
OPENAI_BASE_URL=https://api.openai.com/v1

# 安全配置
JWT_SECRET=your-secure-secret-key
```

#### 资源限制调整
在脚本中修改 `--memory` 和 `--cpus` 参数来调整资源限制。

#### 端口映射自定义
修改脚本中的端口映射来避免冲突：
- Web: 5700 → 8080
- API: 5800 → 8081
- 数据库: 35432 → 15432

## 部署流程图

```mermaid
graph TD
    A[开始部署] --> B[检查系统要求]
    B --> C[创建Pod和网络]
    C --> D[构建应用镜像]
    D --> E[启动中间件服务]
    E --> F[等待中间件就绪]
    F --> G[启动应用服务]
    G --> H[验证部署状态]
    H --> I[部署完成]

    B --> B1[检查Podman版本]
    B --> B2[检查系统资源]
    B --> B3[检查网络连接]

    E --> E1[PostgreSQL]
    E --> E2[Redis]
    E --> E3[MinIO]
    E --> E4[Qdrant]
    E --> E5[SearXNG]

    G --> G1[API服务]
    G --> G2[Web服务]

    H --> H1[端口连通性测试]
    H --> H2[HTTP服务测试]
    H --> H3[数据库连接测试]
```

## 技术支持

如遇问题，请查看：
1. 项目文档: https://github.com/refly-ai/refly
2. 提交Issue: https://github.com/refly-ai/refly/issues
3. 查看部署日志: `./scripts/show-logs.sh all`
4. 运行诊断: `./scripts/05-verify-deployment.sh`
